<template>
  <section>
    <a-card size="small" title="划款通知" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">

          <!-- 款项类型 -->
          <a-form-item name="paymentType" :label="'款项类型'" class="grid-item merge-3" :colon="false">
            <a-checkbox-group
              :disabled="fieldDisabled"
              v-model:value="formData.paymentType"
              @change="handlePaymentTypeChange"
            >
              <a-checkbox value="货款">货款</a-checkbox>
              <a-checkbox value="进出口公司代理费">进出口公司代理费</a-checkbox>
              <a-checkbox value="总公司代理费">总公司代理费</a-checkbox>
              <a-checkbox value="货代费">货代费</a-checkbox>
              <a-checkbox value="保险费">保险费</a-checkbox>
            </a-checkbox-group>
          </a-form-item>

          <!-- 合同金额 -->
          <a-form-item name="contractAmount" :label="'合同金额'" class="grid-item" :colon="false" required>
            <a-input-number
              :disabled="true"
              size="small"
              v-model:value="formData.contractAmount"
              :precision="6"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              style="width: 100%"
              placeholder="默认汇总表体金额"
            />
          </a-form-item>

          <!-- 汇率 -->
          <a-form-item name="exchangeRate" :label="'汇率'" class="grid-item" :colon="false" required>
            <a-input-number
              :disabled="fieldDisabled"
              size="small"
              v-model:value="formData.exchangeRate"
              :precision="6"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              style="width: 100%"
              placeholder="请输入汇率"
              @blur="calculateFees"
            />
          </a-form-item>

          <!-- 进出口公司代理费率% -->
          <a-form-item name="importExportAgentRate" :label="'进出口公司代理费率%'" class="grid-item" :colon="false" required>
            <a-input-number
              :disabled="true"
              size="small"
              v-model:value="formData.importExportAgentRate"
              :precision="4"
              :formatter="value => value ? `${value}%` : ''"
              :parser="value => value.replace('%', '')"
              style="width: 100%"
              placeholder="根据业务类型自动带出"
            />
          </a-form-item>

          <!-- 进出口公司代理费 -->
          <a-form-item name="importExportAgentFee" :label="'进出口公司代理费'" class="grid-item" :colon="false" required>
            <a-input-number
              :disabled="true"
              size="small"
              v-model:value="formData.importExportAgentFee"
              :precision="2"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              style="width: 100%"
              placeholder="系统自动计算"
            />
          </a-form-item>

          <!-- 总公司代理费率% -->
          <a-form-item name="headOfficeAgentRate" :label="'总公司代理费率%'" class="grid-item" :colon="false" required>
            <a-input-number
              :disabled="true"
              size="small"
              v-model:value="formData.headOfficeAgentRate"
              :precision="4"
              :formatter="value => value ? `${value}%` : ''"
              :parser="value => value.replace('%', '')"
              style="width: 100%"
              placeholder="根据业务类型自动带出"
            />
          </a-form-item>

          <!-- 总公司代理费 -->
          <a-form-item name="headOfficeAgentFee" :label="'总公司代理费'" class="grid-item" :colon="false" required>
            <a-input-number
              :disabled="true"
              size="small"
              v-model:value="formData.headOfficeAgentFee"
              :precision="2"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              style="width: 100%"
              placeholder="系统自动计算"
            />
          </a-form-item>

          <!-- 计费箱数 -->
          <a-form-item name="chargeContainerCount" :label="'计费箱数'" class="grid-item" :colon="false" required>
            <a-input-number
              :disabled="fieldDisabled"
              size="small"
              v-model:value="formData.chargeContainerCount"
              :precision="4"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              style="width: 100%"
              placeholder="请输入计费箱数"
              @blur="calculateFreightFee"
            />
          </a-form-item>

          <!-- 通关费 -->
          <a-form-item name="customsClearanceFee" :label="'通关费'" class="grid-item" :colon="false">
            <a-input-number
              :disabled="fieldDisabled"
              size="small"
              v-model:value="formData.customsClearanceFee"
              :precision="4"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              style="width: 100%"
              placeholder="根据业务类型自动带出"
              @blur="calculateFreightFee"
            />
          </a-form-item>

          <!-- 验柜服务费 -->
          <a-form-item name="containerInspectionFee" :label="'验柜服务费'" class="grid-item" :colon="false">
            <a-input-number
              :disabled="fieldDisabled"
              size="small"
              v-model:value="formData.containerInspectionFee"
              :precision="4"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              style="width: 100%"
              placeholder="根据业务类型自动带出"
              @blur="calculateFreightFee"
            />
          </a-form-item>

          <!-- 货代费用 -->
          <a-form-item name="freightForwardingFee" :label="'货代费用'" class="grid-item" :colon="false" required>
            <a-input-number
              :disabled="true"
              size="small"
              v-model:value="formData.freightForwardingFee"
              :precision="2"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              style="width: 100%"
              placeholder="系统自动计算"
            />
          </a-form-item>

          <!-- 保险费率 -->
          <a-form-item name="insuranceRate" :label="'保险费率'" class="grid-item" :colon="false">
            <a-input-number
              :disabled="true"
              size="small"
              v-model:value="formData.insuranceRate"
              :precision="4"
              :formatter="value => value ? `${value}%` : ''"
              :parser="value => value.replace('%', '')"
              style="width: 100%"
              placeholder="根据业务类型自动带出"
            />
          </a-form-item>

          <!-- 保险费 -->
          <a-form-item name="insuranceFee" :label="'保险费'" class="grid-item" :colon="false">
            <a-input-number
              :disabled="true"
              size="small"
              v-model:value="formData.insuranceFee"
              :precision="2"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              style="width: 100%"
              placeholder="系统自动计算"
            />
          </a-form-item>

          <!-- 划款金额（RMB） -->
          <a-form-item name="remittanceAmountRmb" :label="'划款金额（RMB）'" class="grid-item" :colon="false" required>
            <a-input-number
              :disabled="fieldDisabled"
              size="small"
              v-model:value="formData.remittanceAmountRmb"
              :precision="2"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              style="width: 100%"
              placeholder="系统自动计算，允许修改"
            />
          </a-form-item>

          <!-- 备注 -->
          <a-form-item name="remark" :label="'备注'" class="grid-item merge-3" :colon="false">
            <a-textarea
              :disabled="fieldDisabled"
              v-model:value="formData.remark"
              :rows="3"
              maxlength="200"
              placeholder="请输入备注"
            />
          </a-form-item>

        </a-form>
      </div>

      <!-- 操作按钮 -->
      <div class="cs-form-action" v-if="!fieldDisabled">
        <a-button type="primary" @click="handlerSave" :loading="saveLoading">保存</a-button>
        <a-button @click="onBack">返回</a-button>
      </div>
    </a-card>
  </section>
</template>

<script setup>
import { computed, onMounted, reactive, ref } from "vue";
import { editStatus } from "@/view/common/constant";
import { message } from "ant-design-vue";
import {
  insertEquipmentPlanPayNotify,
  updateEquipmentPlanPayNotify,
  getTransMes
} from "@/api/equipment/equipmentPlanApi";

defineOptions({
  name: 'BizIEquipmentPlanHeadPayNotifyEdit'
})

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => ({})
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const onBack = () => {
  emit('onEditBack', true);
};

// 是否禁用
const showDisable = ref(false)
const saveLoading = ref(false)

// 计算最终禁用状态
const fieldDisabled = computed(() => {
  return showDisable.value || props.editConfig.editStatus === editStatus.SHOW
})

// 表单引用
const formRef = ref();

// 表单数据
const formData = reactive({
  id: '',
  headId: '',
  serialNo: '',
  paymentType: [],
  contractAmount: null,
  exchangeRate: null,
  importExportAgentRate: null,
  importExportAgentFee: null,
  headOfficeAgentRate: null,
  headOfficeAgentFee: null,
  chargeContainerCount: null,
  customsClearanceFee: null,
  containerInspectionFee: null,
  freightForwardingFee: null,
  insuranceRate: null,
  insuranceFee: null,
  remittanceAmountRmb: null,
  remark: ''
})

// 表单校验规则
const rules = {
  paymentType: [
    { required: true, message: '款项类型不能为空！', trigger: 'change' }
  ],
  contractAmount: [
    { required: true, message: '合同金额不能为空！', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value && !/^\d{1,13}(\.\d{1,6})?$/.test(value.toString())) {
          return Promise.reject('合同金额必须为数字,整数位最大13位,小数最大6位!');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  exchangeRate: [
    { required: true, message: '汇率不能为空！', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value && !/^\d{1,13}(\.\d{1,6})?$/.test(value.toString())) {
          return Promise.reject('汇率必须为数字,整数位最大13位,小数最大6位!');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  importExportAgentRate: [
    { required: true, message: '进出口公司代理费率%不能为空！', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value && !/^\d{1,15}(\.\d{1,4})?$/.test(value.toString())) {
          return Promise.reject('进出口公司代理费率%必须为数字,整数位最大15位,小数最大4位!');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  importExportAgentFee: [
    { required: true, message: '进出口公司代理费不能为空！', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value && !/^\d{1,17}(\.\d{1,2})?$/.test(value.toString())) {
          return Promise.reject('进出口公司代理费必须为数字,整数位最大17位,小数最大2位!');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  headOfficeAgentRate: [
    { required: true, message: '总公司代理费率%不能为空！', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value && !/^\d{1,15}(\.\d{1,4})?$/.test(value.toString())) {
          return Promise.reject('总公司代理费率%必须为数字,整数位最大15位,小数最大4位!');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  headOfficeAgentFee: [
    { required: true, message: '总公司代理费不能为空！', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value && !/^\d{1,17}(\.\d{1,2})?$/.test(value.toString())) {
          return Promise.reject('总公司代理费必须为数字,整数位最大17位,小数最大2位!');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  chargeContainerCount: [
    { required: true, message: '计费箱数不能为空！', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value && !/^\d{1,15}(\.\d{1,4})?$/.test(value.toString())) {
          return Promise.reject('计费箱数必须为数字,整数位最大15位,小数最大4位!');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  freightForwardingFee: [
    { required: true, message: '货代费用不能为空！', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value && !/^\d{1,17}(\.\d{1,2})?$/.test(value.toString())) {
          return Promise.reject('货代费用必须为数字,整数位最大17位,小数最大2位!');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  remittanceAmountRmb: [
    { required: true, message: '划款金额不能为空！', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value && !/^\d{1,17}(\.\d{1,2})?$/.test(value.toString())) {
          return Promise.reject('划款金额（RMB）必须为数字,整数位最大17位,小数最大2位!');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  // 可选字段的验证规则
  customsClearanceFee: [
    {
      validator: (rule, value) => {
        if (value && !/^\d{1,15}(\.\d{1,4})?$/.test(value.toString())) {
          return Promise.reject('通关费必须为数字,整数位最大15位,小数最大4位!');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  containerInspectionFee: [
    {
      validator: (rule, value) => {
        if (value && !/^\d{1,15}(\.\d{1,4})?$/.test(value.toString())) {
          return Promise.reject('验柜服务费必须为数字,整数位最大15位,小数最大4位!');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  insuranceRate: [
    {
      validator: (rule, value) => {
        if (value && !/^\d{1,15}(\.\d{1,4})?$/.test(value.toString())) {
          return Promise.reject('保险费率必须为数字,整数位最大15位,小数最大4位!');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  insuranceFee: [
    {
      validator: (rule, value) => {
        if (value && !/^\d{1,17}(\.\d{1,2})?$/.test(value.toString())) {
          return Promise.reject('保险费必须为数字,整数位最大17位,小数最大2位!');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  remark: [
    { max: 200, message: '备注长度不能超过200位字节长度!', trigger: 'blur' }
  ]
}

// 款项类型变化处理
const handlePaymentTypeChange = (checkedValues) => {
  console.log('选中的款项类型:', checkedValues);
  calculateRemittanceAmount();
}

// 计算代理费
const calculateFees = () => {
  if (formData.contractAmount && formData.exchangeRate) {
    const contractAmount = parseFloat(formData.contractAmount) || 0;
    const exchangeRate = parseFloat(formData.exchangeRate) || 0;

    // 计算进出口公司代理费
    if (formData.importExportAgentRate) {
      const rate = parseFloat(formData.importExportAgentRate) || 0;
      formData.importExportAgentFee = parseFloat((contractAmount * exchangeRate * rate / 100).toFixed(2));
    }

    // 计算总公司代理费
    if (formData.headOfficeAgentRate) {
      const rate = parseFloat(formData.headOfficeAgentRate) || 0;
      formData.headOfficeAgentFee = parseFloat((contractAmount * exchangeRate * rate / 100).toFixed(2));
    }

    // 计算保险费（合同金额上浮10%）
    if (formData.insuranceRate) {
      const rate = parseFloat(formData.insuranceRate) || 0;
      formData.insuranceFee = parseFloat((contractAmount * 1.1 * exchangeRate * rate / 100).toFixed(2));
    }

    // 重新计算货代费用
    calculateFreightFee();
  }
}

// 计算货代费用
const calculateFreightFee = () => {
  // 货代费用 = 求和（箱型对应的（国际运费+港杂费+陆运费）*计费箱数）+通关费+验柜服务费
  let freightFee = 0;

  // 目前简化计算，只计算通关费和验柜服务费
  // 实际应该根据箱型参数表和计费箱数计算国际运费、港杂费、陆运费
  if (formData.customsClearanceFee) {
    freightFee += parseFloat(formData.customsClearanceFee) || 0;
  }
  if (formData.containerInspectionFee) {
    freightFee += parseFloat(formData.containerInspectionFee) || 0;
  }

  formData.freightForwardingFee = freightFee > 0 ? freightFee.toFixed(2) : null;
  calculateRemittanceAmount();
}

// 计算划款金额
const calculateRemittanceAmount = () => {
  if (!formData.contractAmount || !formData.exchangeRate) return;

  let amount = 0;
  const contractAmount = parseFloat(formData.contractAmount) || 0;
  const exchangeRate = parseFloat(formData.exchangeRate) || 0;

  // 根据选中的款项类型计算对应金额
  if (formData.paymentType.includes('货款')) {
    amount += contractAmount * exchangeRate;
  }

  if (formData.paymentType.includes('进出口公司代理费') && formData.importExportAgentFee) {
    amount += parseFloat(formData.importExportAgentFee) || 0;
  }

  if (formData.paymentType.includes('总公司代理费') && formData.headOfficeAgentFee) {
    amount += parseFloat(formData.headOfficeAgentFee) || 0;
  }

  if (formData.paymentType.includes('货代费') && formData.freightForwardingFee) {
    amount += parseFloat(formData.freightForwardingFee) || 0;
  }

  if (formData.paymentType.includes('保险费') && formData.insuranceFee) {
    amount += parseFloat(formData.insuranceFee) || 0;
  }

  // 进位取整逻辑
  formData.remittanceAmountRmb = amount > 0 ? roundAmount(amount) : null;
}

// 划款金额进位取整
const roundAmount = (amount) => {
  if (amount >= 10000000) { // 千万级，以百万进位取整
    return Math.ceil(amount / 1000000) * 1000000;
  } else if (amount >= 1000000) { // 百万级，以十万进位取整
    return Math.ceil(amount / 100000) * 100000;
  } else { // 百万级以下，以万进位取整
    return Math.ceil(amount / 10000) * 10000;
  }
}

// 保存操作
const handlerSave = async () => {
  try {
    saveLoading.value = true;
    await formRef.value.validate();

    // 检查是否有表头ID
    if (!formData.headId) {
      message.error('缺少表头数据，无法保存');
      return;
    }

    // 准备保存数据
    const saveData = {
      ...formData,
      paymentType: formData.paymentType.join(',') // 将数组转换为逗号分隔的字符串
    };

    let result;
    if (formData.id) {
      // 更新
      result = await updateEquipmentPlanPayNotify(formData.id, saveData);
    } else {
      // 新增
      result = await insertEquipmentPlanPayNotify(saveData);
    }

    if (result.code === 200) {
      message.success('保存成功');
      if (result.data && result.data.id) {
        formData.id = result.data.id;
      }
      // 保存成功后返回列表
      onBack();
    } else {
      message.error(result.message || '保存失败');
    }
  } catch (error) {
    console.error('保存失败', error);
    message.error('保存失败');
  } finally {
    saveLoading.value = false;
  }
}

// 初始化默认值
const initDefaultValues = async () => {
  if (props.editConfig.editData && props.editConfig.editData.headId) {
    try {
      // 获取表头数据中的业务类型
      const businessType = props.editConfig.editData.businessType || '3'; // 默认为3-国营贸易进口烟机设备
      const headId = props.editConfig.editData.headId;

      console.log('调用getTransMes接口，参数:', { headId, businessType });

      // 调用后端接口获取划款参数信息
      const result = await getTransMes(headId, businessType);

      if (result.code === 200 && result.data) {
        const data = result.data;
        console.log('getTransMes接口返回数据:', data);

        // 填充从后端获取的数据
        // 总公司代理费率%
        if (data.headOfficeAgentRate !== undefined && data.headOfficeAgentRate !== null) {
          formData.headOfficeAgentRate = parseFloat(data.headOfficeAgentRate);
        }

        // 进出口公司代理费率%
        if (data.importExportAgentRate !== undefined && data.importExportAgentRate !== null) {
          formData.importExportAgentRate = parseFloat(data.importExportAgentRate);
        }

        // 通关费
        if (data.customsClearanceFee !== undefined && data.customsClearanceFee !== null) {
          formData.customsClearanceFee = parseFloat(data.customsClearanceFee);
        }

        // 验柜服务费
        if (data.containerInspectionFee !== undefined && data.containerInspectionFee !== null) {
          formData.containerInspectionFee = parseFloat(data.containerInspectionFee);
        }

        // 保险费率
        if (data.insuranceRate !== undefined && data.insuranceRate !== null) {
          formData.insuranceRate = parseFloat(data.insuranceRate);
        }

        // 合同金额（表体汇总金额）
        if (data.contractAmount !== undefined && data.contractAmount !== null) {
          formData.contractAmount = parseFloat(data.contractAmount);
        }

        // 汇率
        if (data.exchangeRate !== undefined && data.exchangeRate !== null) {
          formData.exchangeRate = parseFloat(data.exchangeRate);
        }

        // 计算各种费用
        calculateFees();

        console.log('默认数据填充完成:', formData);
      } else {
        console.error('获取划款参数失败:', result.message);
        message.error(result.message || '获取划款参数失败');
      }
    } catch (error) {
      console.error('调用getTransMes接口失败:', error);
      message.error('获取划款参数失败');
    }
  }
}

// 组件挂载时初始化数据
onMounted(async () => {
  if (props.editConfig.editStatus === editStatus.EDIT && props.editConfig.editData) {
    // 编辑模式，加载现有数据
    Object.assign(formData, {
      ...props.editConfig.editData,
      paymentType: props.editConfig.editData.paymentType ? props.editConfig.editData.paymentType.split(',') : []
    });
  } else {
    // 新增模式，初始化默认值
    formData.headId = props.editConfig.editData.headId;
    await initDefaultValues();
  }
})
</script>

<style lang="less" scoped>
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.grid-item {
  grid-column: span 1;
}

.merge-3 {
  grid-column: span 3;
}

.cs-form-action {
  margin-top: 16px;
  text-align: center;
}

.cs-form-action .ant-btn {
  margin: 0 8px;
}
</style>
